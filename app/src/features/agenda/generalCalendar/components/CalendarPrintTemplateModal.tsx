import React, { useState, useEffect } from "react";
import {
    Box,
    DialogContent,
    Dialog,
    DialogTitle,
    DialogActions,
    DialogContentText,
    Divider,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Radio,
    Typography,
} from "@vapor/react-material";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTranslation } from "react-i18next";
import { Close } from "@mui/icons-material";
import { useGetCalendarPrintTemplates } from "../hooks/useGetPrintTemplates";
import type { IPrintTemplatesRespose } from "../typings/generalCalendar.interface";

interface CalendarPrintTemplateModalProps {
    open: boolean;
    onClose: () => void;
    query: any;
}

const CalendarPrintTemplateModal: React.FC<CalendarPrintTemplateModalProps> = ({
    open,
    onClose,
    query,
}) => {
    const { t } = useTranslation();
    const [selectedValue, setSelectedValue] = useState<string>("0");
    const [templates, setTemplates] = useState<IPrintTemplatesRespose[]>([]);

    const templatesResponse = useGetCalendarPrintTemplates({
        fetch: open,
    });

    useEffect(() => {
        if (templatesResponse.hasLoaded && templatesResponse.data) {
            setTemplates(templatesResponse.data);
        }
    }, [templatesResponse.hasLoaded, templatesResponse.data]);

    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSelectedValue(event.target.value);
    };

    const handleConfirm = async () => {
        if (selectedValue && selectedValue !== "0") {
            // TODO: Implement the actual print functionality
            // This should call the appropriate API endpoint to generate the print
            console.log("Selected template ID:", selectedValue);
            console.log("Query parameters:", query);
            
            // For now, just close the modal
            onClose();
        }
    };

    const handleClose = () => {
        setSelectedValue("0");
        onClose();
    };

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            aria-labelledby="calendar-print-template-dialog-title"
            aria-describedby="calendar-print-template-dialog-description"
            maxWidth="md"
            fullWidth
        >
            <DialogTitle id="calendar-print-template-dialog-title">
                <Box display="flex" justifyContent="space-between" alignItems="center">
                    <Typography variant="h6">
                        {t("Scegli il template da utilizzare")}
                    </Typography>
                    <IconButton
                        color="primary"
                        onClick={handleClose}
                        size="small"
                    >
                        <Close />
                    </IconButton>
                </Box>
            </DialogTitle>
            <Divider className="MuiDivider-VaporLight" />
            <DialogContent>
                <DialogContentText id="calendar-print-template-dialog-description">
                    <Box display="flex" gap={1} sx={{ mt: 2 }}>
                        <TableContainer component={Paper}>
                            <Table>
                                <TableHead>
                                    <TableRow>
                                        <TableCell style={{ textAlign: "center" }}>
                                            {/* Radio button column */}
                                        </TableCell>
                                        <TableCell style={{ textAlign: "center" }}>
                                            {t("Nome File")}
                                        </TableCell>
                                        <TableCell style={{ textAlign: "center" }}>
                                            {t("Titolo")}
                                        </TableCell>
                                        <TableCell style={{ textAlign: "center" }}>
                                            {t("Data Caricamento")}
                                        </TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {templatesResponse.loading ? (
                                        <TableRow>
                                            <TableCell colSpan={4} style={{ textAlign: "center" }}>
                                                {t("Caricamento...")}
                                            </TableCell>
                                        </TableRow>
                                    ) : templates.length > 0 ? (
                                        templates.map((template: IPrintTemplatesRespose) => (
                                            <TableRow key={template.id}>
                                                <TableCell width="10%">
                                                    <Radio
                                                        checked={selectedValue === template.id}
                                                        onChange={handleChange}
                                                        value={template.id}
                                                        name="template-radio-buttons"
                                                    />
                                                </TableCell>
                                                <TableCell width="30%">
                                                    {template.filename}
                                                </TableCell>
                                                <TableCell width="30%">
                                                    {template.title}
                                                </TableCell>
                                                <TableCell width="30%">
                                                    {template.upload_date}
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    ) : (
                                        <TableRow>
                                            <TableCell colSpan={4} style={{ textAlign: "center" }}>
                                                {t("Non ci sono template caricati per questa sezione")}
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    </Box>
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button variant="outlined" onClick={handleClose}>
                    {t("Annulla")}
                </Button>
                <Button 
                    variant="contained" 
                    onClick={handleConfirm}
                    disabled={selectedValue === "0" || templatesResponse.loading}
                >
                    {t("Conferma")}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default CalendarPrintTemplateModal;

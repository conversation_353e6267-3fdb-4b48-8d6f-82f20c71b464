import {
    Autocomplete,
    FormControl,
    InputLabel,
    TextField,
} from "@vapor/react-material";
import React, { useState, ReactNode } from "react";
import { useTranslation } from "@1f/react-sdk";

interface CustomSelectProps {
    id?: string;
    label?: React.ReactNode;
    options?: Array<{
        label: string;
        value: any;
        selected?: boolean;
        childElement?: React.ReactElement;
        childIndex?: number;
        group?: string;
    } | null>;
    onChange: ((name: string, value: any) => void) | ((event: React.ChangeEvent<any>) => void);
    placeholder?: string;
    selectedValue?: any;
    value?: any;
    name: string;
    width?: string;
    style?: React.CSSProperties;
    children?: ReactNode;
    maxMenuHeight?: number;
    hideLabel?: boolean;
    group?: string;
    dataSource?: Array<any>;
    valueKey?: string;
    renderByGroup?: (option: any) => string;
    isOptionDisabled?: (option: any) => boolean;
    disabled?: boolean;
}

export const CustomSelect = (props: CustomSelectProps) => {
    const [selectedValue, setSelectedValue] = React.useState<any>(null);
    const [isOpen, setIsOpen] = useState(false);
    const {
        label,
        options = [],
        onChange,
        placeholder,
        selectedValue: valueObject,
        value: simpleValue,
        name,
        width,
        style = {},
        children,
        maxMenuHeight = 200,
        hideLabel = false,
        group,
        dataSource,
        valueKey = 'id',
        renderByGroup,
        isOptionDisabled,
        disabled,
    } = props;

    const { t } = useTranslation();

    const selectedOptionValue = React.useMemo(
        () => options?.filter((v: any) => v && v.selected)?.[0] || null,
        [options]
    );

    const childrenOptions = React.useMemo(() => {
        if (!children) return [];

        const childrenArray = React.Children.toArray(children);

        return childrenArray.map((child: any, index) => {
            if (React.isValidElement(child) && child.props) {
                const props = child.props as Record<string, any>;
                if ('value' in props && props.children !== undefined) {
                    let groupValue = undefined;
                    if (dataSource && group && props.value && props.value !== '-1') {
                        const sourceItem = dataSource.find(item => 
                            String(item[valueKey]) === String(props.value)
                        );
                        if (sourceItem && sourceItem[group] !== undefined) {
                            groupValue = sourceItem[group];
                        }
                    }

                    return {
                        label: String(props.children),
                        value: props.value,
                        childElement: child,
                        childIndex: index,
                        [group as string]: groupValue,
                    };
                }
            }
            return null;
        }).filter(Boolean);
    }, [children, dataSource, group, valueKey]);

    const combinedOptions = React.useMemo(() => {
        if (childrenOptions.length === 0) return options;
        if (options.length === 0) return childrenOptions;
        return [...options, ...childrenOptions];
    }, [options, childrenOptions]);

    const findOptionByValue = React.useCallback((val: any, opts: any[]) => {
        if (val === undefined || val === null) return null;
        return opts.find(opt => String(opt.value) === String(val)) || null;
    }, []);

    React.useEffect(() => {
        if (valueObject) {
            setSelectedValue(valueObject);
        } else if (simpleValue !== undefined) {
            const matchingOption = findOptionByValue(simpleValue, combinedOptions);
            if (matchingOption) {
                setSelectedValue(matchingOption);
            }
        }
    }, [valueObject, simpleValue, findOptionByValue, combinedOptions]);

    const handleChange = (_event: React.SyntheticEvent, newValue: any) => {
        setSelectedValue(newValue);

        if (onChange.length === 1) {
            const syntheticEvent = {
                target: {
                    name,
                    value: newValue?.value
                }
            };
            (onChange as (event: React.ChangeEvent<any>) => void)(syntheticEvent as any);
        } else {
            (onChange as (name: string, value: any) => void)(name, newValue);
        }
    };

   
    return (
        <FormControl style={style}>
            {label && !hideLabel && <InputLabel>{label}</InputLabel>}
            <Autocomplete
                disabled={disabled}
                id={props.id || props.name}
                options={combinedOptions}
                value={selectedValue || selectedOptionValue || null}
                getOptionLabel={(option: any) => option?.label || ""}
                isOptionEqualToValue={(option: any, value: any) =>
                    option?.value === value?.value
                }
                getOptionDisabled={isOptionDisabled}
                loadingText={t("Caricamento...")}
                noOptionsText={t("Nessuna opzione")}
                open={isOpen}
                onOpen={() => setIsOpen(true)}
                onClose={() => setIsOpen(false)}
                disablePortal={false}
                componentsProps={{
                    popupIndicator: {
                        title: isOpen ? t("Chiudi") : t("Apri"),
                    }
                }}
                sx={{
                    "& .MuiAutocomplete-listbox": {
                        maxHeight: `${maxMenuHeight}px`,
                        overflowY: "auto"
                    }
                }}
                onChange={handleChange}
                {...(group ? { groupBy: renderByGroup } : {})}
                renderOption={(props: any, option: any) => {
                    const isDisabled = isOptionDisabled ? isOptionDisabled(option) : false;
                    
                    if (isDisabled) {
                        props.className = `${props.className || ''} Mui-disabled`;
                        props['aria-disabled'] = true;
                    }

                    if (option.childElement) {
                        const child = option.childElement;
                        return React.cloneElement(child, {
                            ...props,
                            key: `child-${option.childIndex}`,
                            disabled: isDisabled
                        });
                    }

                    return (
                        <li key={option.value} {...props}>
                            {option?.label}
                        </li>
                    );
                }}
                renderInput={(params: any) => (
                    <TextField
                        {...params}
                        placeholder={placeholder ?? t("")}
                    />
                )}
                style={{
                    width: width ?? "100%"
                }}
            />
        </FormControl>
    );
};

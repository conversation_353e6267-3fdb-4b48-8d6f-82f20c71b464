import { Button, CircularProgress, ButtonProps } from "@vapor/react-material";

interface SpinnerButtonProps extends ButtonProps {
    label: string;
    isLoading: boolean;
}

const SpinnerButton = ({
    label,
    isLoading,
    ...otherProps
}: SpinnerButtonProps) => {
    return (
        <Button
            disabled={isLoading || otherProps.disabled}
            {...otherProps}>
            {isLoading ? (
                <CircularProgress
                    color="inherit"
                    size={24}
                />
            ) : (
                label
            )}
        </Button>
    );
};
export default SpinnerButton;

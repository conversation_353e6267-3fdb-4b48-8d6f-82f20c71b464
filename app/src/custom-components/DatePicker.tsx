import {
    DatePicker,
    LocalizationProvider,
    AdapterDateFns,
} from "@vapor/react-x-date-pickers";
import { SxProps } from "@mui/system";
import { it } from "date-fns/locale"; // Import Italian locale for DD/MM/YYYY format
import { Controller } from "react-hook-form";
import moment from "moment";

interface DatePickerProps {
    label: string;
    name: string;
    value?: Date | null | string;
    sx?: SxProps;
    control?: any;
    onChange?: (name: string, date: Date) => void;
    placeholder?: string;
    minDate?: Date | null;
}

export const DatePickerUi = (props: DatePickerProps) => {
    const { name, label, value, sx, onChange, control, placeholder } = props;

    return (
        <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={it}>
            {control ? (
                <Controller
                    name={name}
                    control={control}
                    render={({ field: { onChange, value } }) => {
                        return (
                            <DatePicker
                                label={label}
                                onChange={onChange}
                                format="dd/MM/yyyy"
                                value={moment(value, "DD/MM/YYYY").toDate()}
                                slotProps={{
                                    textField: {
                                        fullWidth: true,
                                        name: name,
                                        error: false,
                                        placeholder:
                                            placeholder || "DD/MM/YYYY",
                                        sx,
                                    },
                                }}
                                minDate={props.minDate ? props.minDate : undefined}
                            />
                        );
                    }}
                />
            ) : (
                <DatePicker
                    label={label}
                    value={value ? moment(value, "DD/MM/YYYY").toDate() : null}
                    onChange={(date) =>
                        onChange && onChange(name, date as Date)
                    }
                    format="dd/MM/yyyy"
                    slotProps={{
                        textField: {
                            fullWidth: true,
                            name: name,
                            placeholder: placeholder || "DD/MM/YYYY",
                            sx,
                        },
                    }}
                    minDate={props.minDate ? props.minDate : undefined}
                />
            )}
        </LocalizationProvider>
    );
};

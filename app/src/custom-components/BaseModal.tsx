import {
    DialogContent,
    Dialog,
    Icon<PERSON>utton,
    DialogTitle,
    Dialog<PERSON><PERSON>,
    Divider,
    Button,
} from "@vapor/react-material";
import { Close } from "@mui/icons-material";
type DividerVariants = "fullWidth" | "inset" | "middle";
type ButtonColors =
    | "success"
    | "info"
    | "warning"
    | "error"
    | "inherit"
    | "primary"
    | "secondary";
interface IBaseModalProps {
    open: boolean;
    title: string;
    content: React.ReactNode;
    decline?: string;
    agree?: string;
    dividerVariant?: DividerVariants;
    colorConfirmButton?: ButtonColors;
    handleDecline?: (param?: any) => void;
    handleAgree?: () => void;
    isFooter: boolean;
}

export default function BaseModal(props: IBaseModalProps) {
    const {
        open,
        title,
        content,
        decline,
        agree,
        dividerVariant = "fullWidth",
        colorConfirmButton = "primary",
        handleDecline,
        handleAgree,
        isFooter,
    } = props;
    return (
        <Dialog
            open={open}
            onClose={handleDecline}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
        >
            <DialogTitle>
                {title}
                <IconButton color="primary" onClick={handleDecline}>
                    <Close />
                </IconButton>
            </DialogTitle>
            <Divider
                variant={
                    dividerVariant !== undefined ? dividerVariant : "fullWidth"
                }
            />
            <DialogContent>{content}</DialogContent>
            {isFooter ? (
                <DialogActions>
                    {decline && (
                        <Button variant="outlined" onClick={handleDecline}>
                            {decline}
                        </Button>
                    )}
                    {agree !== undefined && (
                        <Button
                            variant="contained"
                            onClick={handleAgree}
                            color={colorConfirmButton}
                        >
                            {agree}
                        </Button>
                    )}
                </DialogActions>
            ) : (
                ""
            )}
        </Dialog>
    );
}

import {
    DialogContent,
    Dialog,
    DialogTitle,
    DialogActions,
    Divider,
    Button,
    ButtonProps,
    DividerProps,
    IconButton,
    DialogProps,
} from "@vapor/react-material";
import { ReactNode } from "react";
import SpinnerButton from "./SpinnerButton";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faClose } from "@fortawesome/free-solid-svg-icons";

interface IConfirmModalProps {
    open: boolean;
    title: string;
    confirmText?: string;
    decline?: string;
    agree?: string;
    dividerVariant?: DividerProps["variant"];
    colorConfirmButton?: ButtonProps["color"];
    colorDeclineButton?: ButtonProps["color"];
    handleDecline: (param?: any) => void;
    handleAgree: () => void;
    children?: ReactNode;
    loading?: boolean;
    maxWidth?: DialogProps["maxWidth"];
    fullWidth?: DialogProps["fullWidth"];
    customActions?: ReactNode[];
}

export default function ConfirmModal(props: IConfirmModalProps) {
    const {
        open,
        title,
        confirmText,
        decline,
        agree,
        dividerVariant = "fullWidth",
        colorConfirmButton = "primary",
        handleDecline,
        handleAgree,
        colorDeclineButton,
        children,
        loading,
        maxWidth = "xs",
        fullWidth = true,
        customActions,
    } = props;
    return (
        <Dialog
            open={open}
            onClose={handleDecline}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
            maxWidth={maxWidth}
            fullWidth={fullWidth}>
            <DialogTitle>
                {title}
                <IconButton
                    color="primary"
                    onClick={handleDecline}>
                    <FontAwesomeIcon icon={faClose}></FontAwesomeIcon>
                </IconButton>
            </DialogTitle>
            <Divider variant={dividerVariant} />
            <DialogContent>{children ? children : confirmText}</DialogContent>
            <DialogActions>
                {decline && (
                    <Button
                        variant="outlined"
                        onClick={handleDecline}
                        color={colorDeclineButton}>
                        {decline}
                    </Button>
                )}
                {agree && (
                    <SpinnerButton
                        isLoading={loading ? true : false}
                        variant="contained"
                        label={agree}
                        color={colorConfirmButton}
                        onClick={handleAgree}
                    />
                )}
                {customActions}
            </DialogActions>
        </Dialog>
    );
}

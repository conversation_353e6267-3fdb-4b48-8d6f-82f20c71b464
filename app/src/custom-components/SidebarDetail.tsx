import { useState } from "react";
import { Box, Typography, Badge, Tooltip } from "@vapor/react-material";
import { Copy } from "@vapor/react-icons";

const DetailSidebar = (props: any) => {
    const {
        childrenBadgeTitle,
        badgeTitle,
        iconTitle,
        title,
        idLabel,
        id,
        subtitle,
        subtitleLabel,
    } = props;
    const [tooltipOpen, setTooltipOpen] = useState<boolean>(false);
    const [tooltipSubtitleOpen, setTooltipSubtitleOpen] =
        useState<boolean>(false);

    const handleCopy = (text: string, isSubtitle = false) => {
        if (isSubtitle) {
            setTooltipSubtitleOpen(true);
            setTimeout(() => setTooltipSubtitleOpen(false), 1000);
        } else {
            setTooltipOpen(true);
            setTimeout(() => setTooltipOpen(false), 1000);
        }

        navigator.clipboard.writeText(text);
    };

    return (
        <div>
            {badgeTitle && (
                <Box
                    sx={{ mb: 2 }}
                    display="flex"
                    alignItems="center"
                    flexWrap={"wrap"}
                    style={{ backgroundColor: "#f7f7f7" }}
                >
                    <Badge
                        style={{
                            backgroundColor: "#E4EAF0",
                            borderRadius: "4px",
                            padding: "4px 8px",
                            display: "inline-block",
                            marginRight: "15px",
                            marginBottom: "5px",
                        }}
                    >
                        <Typography
                            variant="body2"
                            color="primary.textTitleColor"
                        >
                            {badgeTitle}
                        </Typography>
                    </Badge>
                    {childrenBadgeTitle && (
                        <Badge
                            style={{
                                backgroundColor: "#E4EAF0",
                                borderRadius: "4px",
                                padding: "4px 8px",
                                display: "inline-block",
                                marginBottom: "5px",
                            }}
                        >
                            <Typography
                                variant="body2"
                                color="primary.textTitleColor"
                            >
                                {childrenBadgeTitle}
                            </Typography>
                        </Badge>
                    )}
                </Box>
            )}
            <Box sx={{ mb: 2 }}>
                {iconTitle && title && (
                    <Box sx={{ display: "flex", gap: 1 }}>
                        {iconTitle}
                        <Typography
                            variant="titleSmall"
                            component="div"
                            color="primary.textTitleColor"
                            gutterBottom
                            sx={{ margin: "-2px" }}
                        >
                            {title}
                        </Typography>
                    </Box>
                )}
                {idLabel && id && (
                    <Box sx={{ display: "flex", gap: 3, mt: 2 }}>
                        <Typography
                            variant="body"
                            gutterBottom
                            component="div"
                            color="primary.textDisabledColor"
                        >
                            {idLabel}
                        </Typography>
                        <Typography
                            variant="bodySmall"
                            gutterBottom
                            component="div"
                        >
                            {id}
                        </Typography>

                        <Tooltip
                            title="Copiato"
                            open={tooltipOpen}
                            disableHoverListener
                            disableFocusListener
                            disableTouchListener
                            placement="right"
                        >
                            <span
                                onClick={() => handleCopy(id)}
                                style={{
                                    cursor: "pointer",
                                    paddingBottom: "5px",
                                }}
                            >
                                <Copy color="interactive" />
                            </span>
                        </Tooltip>
                    </Box>
                )}
                {subtitle && subtitleLabel && (
                    <Box sx={{ display: "flex", gap: 3 }}>
                        <Typography
                            variant="body"
                            gutterBottom
                            component="div"
                            color="primary.textDisabledColor"
                        >
                            {subtitleLabel}
                        </Typography>
                        <Typography
                            variant="bodySmall"
                            gutterBottom
                            component="div"
                        >
                            {subtitle}
                        </Typography>

                        <Tooltip
                            title="Copiato"
                            open={tooltipSubtitleOpen}
                            disableHoverListener
                            disableFocusListener
                            disableTouchListener
                            placement="right"
                        >
                            <span
                                onClick={() => handleCopy(subtitle, true)}
                                style={{
                                    cursor: "pointer",
                                    paddingBottom: "5px",
                                }}
                            >
                                <Copy color="interactive" />
                            </span>
                        </Tooltip>
                    </Box>
                )}
            </Box>
            {props.children}
        </div>
    );
};

export default DetailSidebar;
